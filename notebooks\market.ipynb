{"cells": [{"cell_type": "code", "execution_count": 2, "id": "beb74f73", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["dict_keys(['PURR/USDC', 'HFUN/USDC', 'LICK/USDC', 'MANLET/USDC', 'JEFF/USDC', 'SIX/USDC', 'WAGMI/USDC', 'CAPPY/USDC', 'POINTS/USDC', 'TRUMP/USDC', 'GMEOW/USDC', 'PEPE/USDC', 'XULIAN/USDC', 'RUG/USDC', 'ILIENS/USDC', 'FUCKY/USDC', 'CZ/USDC', 'BAGS/USDC', 'ANSEM/USDC', 'TATE/USDC', 'PUMP/USDC', 'KOBE/USDC', 'HAPPY/USDC', 'SCHIZO/USDC', 'SELL/USDC', 'BIGBEN/USDC', 'CATNIP/USDC', 'HBOOST/USDC', 'SUCKY/USDC', 'GUP/USDC', 'FARMED/USDC', 'GPT/USDC', 'PURRPS/USDC', 'BID/USDC', 'HODL/USDC', 'VEGAS/USDC', 'ASI/USDC', 'VAPOR/USDC', 'PANDA/USDC', 'PILL/USDC', 'ADHD/USDC', 'FUN/USDC', 'LADY/USDC', 'MOG/USDC', 'HPEPE/USDC', 'JEET/USDC', 'DROP/USDC', 'MBAPPE/USDC', 'TEST/USDC', 'RAGE/USDC', 'FRAC/USDC', 'ATEHUN/USDC', 'COZY/USDC', 'ARI/USDC', 'WASH/USDC', 'ANT/USDC', 'NFT/USDC', 'RICH/USDC', 'LORA/USDC', 'CATBAL/USDC', 'TJIF/USDC', 'GUESS/USDC', 'MAXI/USDC', 'NMTD/USDC', 'HPUMP/USDC', 'PIGEON/USDC', 'RISE/USDC', 'CINDY/USDC', 'CHINA/USDC', 'STACK/USDC', 'FRIED/USDC', 'NOCEX/USDC', 'RANK/USDC', 'OMNIX/USDC', 'RIP/USDC', 'G/USDC', 'BOZO/USDC', 'SPH/USDC', 'SHOE/USDC', 'MONAD/USDC', 'HOPE/USDC', 'BUSSY/USDC', 'FATCAT/USDC', 'SHREK/USDC', 'PIP/USDC', 'LQNA/USDC', 'NASDAQ/USDC', 'YEETI/USDC', 'SYLVI/USDC', 'FEIT/USDC', 'FRUDO/USDC', 'VIZN/USDC', 'STRICT/USDC', 'AUTIST/USDC', 'MAGA/USDC', 'HGOD/USDC', 'LIQUID/USDC', 'EARTH/USDC', 'UP/USDC', 'NIGGO/USDC', 'HOP/USDC', 'LUCKY/USDC', 'COPE/USDC', 'HPYH/USDC', 'YAP/USDC', 'HYPE/USDC', 'CHEF/USDC', 'WOW/USDC', 'STEEL/USDC', 'RETARD/USDC', 'MEOW/USDC', 'NEIRO/USDC', 'PEAR/USDC', 'HOLD/USDC', 'MUNCH/USDC', 'BERA/USDC', 'GENESY/USDC', 'BUBZ/USDC', 'PICKL/USDC', 'SHEEP/USDC', 'LAUNCH/USDC', 'FARM/USDC', 'FLASK/USDC', 'VAULT/USDC', 'CAT/USDC', 'HYENA/USDC', 'DEPIN/USDC', 'MON/USDC', 'BEATS/USDC', 'ORA/USDC', 'LIQD/USDC', 'H/USDC', 'STAR/USDC', 'SENT/USDC', 'SOLV/USDC', 'FLY/USDC', 'TIME/USDC', 'SOVRN/USDC', 'HWTR/USDC', 'GOD/USDC', 'UBTC/USDC', 'HEAD/USDC', 'VORTX/USDC', 'DEFIN/USDC', 'JPEG/USDC', 'WHYPI/USDC', 'USDE/USDC', 'UETH/USDC', 'USDXL/USDC', 'FEUSD/USDC', 'QUANT/USDC', 'USOL/USDC', 'RAT/USDC', 'TILT/USDC', 'TREND/USDC', 'BUDDY/USDC', 'PRFI/USDC', 'UFART/USDC', 'FUND/USDC', 'DIABLO/USDC', 'PENIS/USDC', 'USDT0/USDC', 'PEG/USDC', 'USH/USDC', 'COOK/USDC', 'RUB/USDC', 'ANON/USDC', 'FRCT/USDC', 'PERP/USDC', 'PURRO/USDC', 'USR/USDC', 'OTTI/USDC', 'USDHL/USDC', 'XAUT0/USDC', 'HORSY/USDC', 'HPENGU/USDC', 'ISLAND/USDC', 'LICKO/USDC', 'UPUMP/USDC', 'USPYX/USDC', 'STLOOP/USDC', 'TRADE/USDC', 'ANZ/USDC', 'UUUSPX/USDC', 'UBONK/USDC', 'LATINA/USDC', 'APU/USDC', 'WOULD/USDC', 'EX/USDC', 'UMOG/USDC', 'HREKT/USDC', 'BTC/USDC:USDC', 'ETH/USDC:USDC', 'ATOM/USDC:USDC', 'MATIC/USDC:USDC', 'DYDX/USDC:USDC', 'SOL/USDC:USDC', 'AVAX/USDC:USDC', 'BNB/USDC:USDC', 'APE/USDC:USDC', 'OP/USDC:USDC', 'LTC/USDC:USDC', 'ARB/USDC:USDC', 'DOGE/USDC:USDC', 'INJ/USDC:USDC', 'SUI/USDC:USDC', 'kPEPE/USDC:USDC', 'CRV/USDC:USDC', 'LDO/USDC:USDC', 'LINK/USDC:USDC', 'STX/USDC:USDC', 'RNDR/USDC:USDC', 'CFX/USDC:USDC', 'FTM/USDC:USDC', 'GMX/USDC:USDC', 'SNX/USDC:USDC', 'XRP/USDC:USDC', 'BCH/USDC:USDC', 'APT/USDC:USDC', 'AAVE/USDC:USDC', 'COMP/USDC:USDC', 'MKR/USDC:USDC', 'WLD/USDC:USDC', 'FXS/USDC:USDC', 'HPOS/USDC:USDC', 'RLB/USDC:USDC', 'UNIBOT/USDC:USDC', 'YGG/USDC:USDC', 'TRX/USDC:USDC', 'kSHIB/USDC:USDC', 'UNI/USDC:USDC', 'SEI/USDC:USDC', 'RUNE/USDC:USDC', 'OX/USDC:USDC', 'FRIEND/USDC:USDC', 'SHIA/USDC:USDC', 'CYBER/USDC:USDC', 'ZRO/USDC:USDC', 'BLZ/USDC:USDC', 'DOT/USDC:USDC', 'BANANA/USDC:USDC', 'TRB/USDC:USDC', 'FTT/USDC:USDC', 'LOOM/USDC:USDC', 'OGN/USDC:USDC', 'RDNT/USDC:USDC', 'ARK/USDC:USDC', 'BNT/USDC:USDC', 'CANTO/USDC:USDC', 'REQ/USDC:USDC', 'BIGTIME/USDC:USDC', 'KAS/USDC:USDC', 'ORBS/USDC:USDC', 'BLUR/USDC:USDC', 'TIA/USDC:USDC', 'BSV/USDC:USDC', 'ADA/USDC:USDC', 'TON/USDC:USDC', 'MINA/USDC:USDC', 'POLYX/USDC:USDC', 'GAS/USDC:USDC', 'PENDLE/USDC:USDC', 'STG/USDC:USDC', 'FET/USDC:USDC', 'STRAX/USDC:USDC', 'NEAR/USDC:USDC', 'MEME/USDC:USDC', 'ORDI/USDC:USDC', 'BADGER/USDC:USDC', 'NEO/USDC:USDC', 'ZEN/USDC:USDC', 'FIL/USDC:USDC', 'PYTH/USDC:USDC', 'SUSHI/USDC:USDC', 'ILV/USDC:USDC', 'IMX/USDC:USDC', 'kBONK/USDC:USDC', 'GMT/USDC:USDC', 'SUPER/USDC:USDC', 'USTC/USDC:USDC', 'NFTI/USDC:USDC', 'JUP/USDC:USDC', 'kLUNC/USDC:USDC', 'RSR/USDC:USDC', 'GALA/USDC:USDC', 'JTO/USDC:USDC', 'NTRN/USDC:USDC', 'ACE/USDC:USDC', 'MAV/USDC:USDC', 'WIF/USDC:USDC', 'CAKE/USDC:USDC', 'PEOPLE/USDC:USDC', 'ENS/USDC:USDC', 'ETC/USDC:USDC', 'XAI/USDC:USDC', 'MANTA/USDC:USDC', 'UMA/USDC:USDC', 'ONDO/USDC:USDC', 'ALT/USDC:USDC', 'ZETA/USDC:USDC', 'DYM/USDC:USDC', 'MAVIA/USDC:USDC', 'W/USDC:USDC', 'PANDORA/USDC:USDC', 'STRK/USDC:USDC', 'PIXEL/USDC:USDC', 'AI/USDC:USDC', 'TAO/USDC:USDC', 'AR/USDC:USDC', 'MYRO/USDC:USDC', 'kFLOKI/USDC:USDC', 'BOME/USDC:USDC', 'ETHFI/USDC:USDC', 'ENA/USDC:USDC', 'MNT/USDC:USDC', 'TNSR/USDC:USDC', 'SAGA/USDC:USDC', 'MERL/USDC:USDC', 'HBAR/USDC:USDC', 'POPCAT/USDC:USDC', 'OMNI/USDC:USDC', 'EIGEN/USDC:USDC', 'REZ/USDC:USDC', 'NOT/USDC:USDC', 'TURBO/USDC:USDC', 'BRETT/USDC:USDC', 'IO/USDC:USDC', 'ZK/USDC:USDC', 'BLAST/USDC:USDC', 'LISTA/USDC:USDC', 'MEW/USDC:USDC', 'RENDER/USDC:USDC', 'kDOGS/USDC:USDC', 'POL/USDC:USDC', 'CATI/USDC:USDC', 'CELO/USDC:USDC', 'HMSTR/USDC:USDC', 'SCR/USDC:USDC', 'NEIROETH/USDC:USDC', 'kNEIRO/USDC:USDC', 'GOAT/USDC:USDC', 'MOODENG/USDC:USDC', 'GRASS/USDC:USDC', 'PURR/USDC:USDC', 'PNUT/USDC:USDC', 'XLM/USDC:USDC', 'CHILLGUY/USDC:USDC', 'SAND/USDC:USDC', 'IOTA/USDC:USDC', 'ALGO/USDC:USDC', 'HYPE/USDC:USDC', 'ME/USDC:USDC', 'MOVE/USDC:USDC', 'VIRTUAL/USDC:USDC', 'PENGU/USDC:USDC', 'USUAL/USDC:USDC', 'FARTCOIN/USDC:USDC', 'AI16Z/USDC:USDC', 'AIXBT/USDC:USDC', 'ZEREBRO/USDC:USDC', 'BIO/USDC:USDC', 'GRIFFAIN/USDC:USDC', 'SPX/USDC:USDC', 'S/USDC:USDC', 'MORPHO/USDC:USDC', 'TRUMP/USDC:USDC', 'MELANIA/USDC:USDC', 'ANIME/USDC:USDC', 'VINE/USDC:USDC', 'VVV/USDC:USDC', 'JELLY/USDC:USDC', 'BERA/USDC:USDC', 'TST/USDC:USDC', 'LAYER/USDC:USDC', 'IP/USDC:USDC', 'OM/USDC:USDC', 'KAITO/USDC:USDC', 'NIL/USDC:USDC', 'PAXG/USDC:USDC', 'PROMPT/USDC:USDC', 'BABY/USDC:USDC', 'WCT/USDC:USDC', 'HYPER/USDC:USDC', 'ZORA/USDC:USDC', 'INIT/USDC:USDC', 'DOOD/USDC:USDC', 'LAUNCHCOIN/USDC:USDC', 'NXPC/USDC:USDC', 'SOPH/USDC:USDC', 'RESOLV/USDC:USDC', 'SYRUP/USDC:USDC', 'PUMP/USDC:USDC'])\n", "                         open      high       low     close      volume\n", "timestamp                                                              \n", "2025-06-06 12:00:00  103880.0  103988.0  103880.0  103927.0   215.87940\n", "2025-06-06 12:15:00  103928.0  103928.0  103764.0  103800.0   245.07328\n", "2025-06-06 12:30:00  103800.0  104271.0  103484.0  103651.0  1229.05748\n", "2025-06-06 12:45:00  103650.0  103920.0  103648.0  103648.0   665.09433\n", "2025-06-06 13:00:00  103649.0  103997.0  103648.0  103940.0   260.32086\n"]}], "source": ["import sys\n", "import os\n", "sys.path.append(os.path.abspath(os.path.join(os.getcwd(), '..')))\n", "from exchange import hyperliquid, fetch_and_prepare_data\n", "import numpy as np\n", "import pandas as pd\n", "\n", "print(hyperliquid.load_markets().keys())\n", "data = fetch_and_prepare_data(\"BTC/USDC:USDC\")\n", "print(data.head())"]}, {"cell_type": "code", "execution_count": null, "id": "dff44dda", "metadata": {}, "outputs": [], "source": ["\n", "# Split data into train and test sets (80/20 split)\n", "\n", "split = 5\n", "data_splits = {}  # Dictionnaire pour stocker les splits\n", "\n", "for i in range(split):\n", "    data_splits[f'data_split_{i}'] = np.array_split(data, split)[i]\n", "\n", "figs = {}\n", "for col in ['close']:\n", "    plot_data = pd.concat([data_splits[f'data_split_{i}'][col].rename(f\"data_split_{i}_{col}\") for i in range(split)], axis=1)\n", "    figs[col] = plot_data.plot(title=f\"{col} par split\")\n", "\n", "# figs['close'].show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "0f3fdede", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Available subplots: dict_keys(['orders', 'trades', 'trade_pnl', 'asset_flow', 'cash_flow', 'assets', 'cash', 'asset_value', 'value', 'cum_returns', 'drawdowns', 'underwater', 'gross_exposure', 'net_exposure'])\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c5a49f4797684f148a80989c1ac28c1b", "version_major": 2, "version_minor": 0}, "text/plain": ["FigureWidget({\n", "    'data': [{'legendgroup': '0',\n", "              'line': {'color': '#1f77b4'},\n", "              'name': 'Close',\n", "              'showlegend': True,\n", "              'type': 'scatter',\n", "              'uid': '3532a426-111d-49f8-9278-2857c20ad3ad',\n", "              'x': array([datetime.datetime(2025, 6, 6, 12, 0),\n", "                          datetime.datetime(2025, 6, 6, 12, 15),\n", "                          datetime.datetime(2025, 6, 6, 12, 30), ...,\n", "                          datetime.datetime(2025, 7, 28, 17, 30),\n", "                          datetime.datetime(2025, 7, 28, 17, 45),\n", "                          datetime.datetime(2025, 7, 28, 18, 0)], dtype=object),\n", "              'xaxis': 'x',\n", "              'y': array([103927., 103800., 103651., ..., 117946., 117732., 117664.]),\n", "              'yaxis': 'y'},\n", "             {'customdata': array([[ 0.        ,  0.09602923,  9.99000999],\n", "                                   [ 2.        ,  0.09578495, 10.16930281],\n", "                                   [ 4.        ,  0.09543903, 10.35221053],\n", "                                   [ 6.        ,  0.09570641, 10.52941847],\n", "                                   [ 8.        ,  0.09517579, 10.73113086],\n", "                                   [10.        ,  0.09442246, 10.99363646],\n", "                                   [12.        ,  0.09421827, 11.18225412],\n", "                                   [14.        ,  0.09423528, 11.40198603]]),\n", "              'hovertemplate': ('Order Id: %{customdata[0]}<br>' ... '<br>Fees: %{customdata[2]:.6f}'),\n", "              'legendgroup': '1',\n", "              'marker': {'color': '#37B13F',\n", "                         'line': {'color': 'rgb(38,123,44)', 'width': 1},\n", "                         'size': 8,\n", "                         'symbol': 'triangle-up'},\n", "              'mode': 'markers',\n", "              'name': 'Buy',\n", "              'type': 'scatter',\n", "              'uid': 'a623d5a7-d54b-465d-99ae-27882fd8d972',\n", "              'x': array([datetime.datetime(2025, 6, 6, 12, 0),\n", "                          datetime.datetime(2025, 6, 8, 16, 15),\n", "                          datetime.datetime(2025, 6, 9, 17, 15),\n", "                          datetime.datetime(2025, 6, 9, 21, 30),\n", "                          datetime.datetime(2025, 7, 10, 16, 30),\n", "                          datetime.datetime(2025, 7, 10, 21, 30),\n", "                          datetime.datetime(2025, 7, 11, 9, 45),\n", "                          datetime.datetime(2025, 7, 14, 3, 45)], dtype=object),\n", "              'xaxis': 'x',\n", "              'y': array([104030.927, 106168.062, 108469.361, 110017.908, 112750.638, 116430.314,\n", "                          118684.566, 120994.874]),\n", "              'yaxis': 'y'},\n", "             {'customdata': array([[ 1.        ,  0.09602923, 10.18966177],\n", "                                   [ 3.        ,  0.09578495, 10.37293568],\n", "                                   [ 5.        ,  0.09543903, 10.55049838],\n", "                                   [ 7.        ,  0.09570641, 10.75261461],\n", "                                   [ 9.        ,  0.09517579, 11.01564575],\n", "                                   [11.        ,  0.09442246, 11.20464101],\n", "                                   [13.        ,  0.09421827, 11.42481282]]),\n", "              'hovertemplate': ('Order Id: %{customdata[0]}<br>' ... '<br>Fees: %{customdata[2]:.6f}'),\n", "              'legendgroup': '2',\n", "              'marker': {'color': '#EA4335',\n", "                         'line': {'color': 'rgb(181,31,18)', 'width': 1},\n", "                         'size': 8,\n", "                         'symbol': 'triangle-down'},\n", "              'mode': 'markers',\n", "              'name': '<PERSON><PERSON>',\n", "              'type': 'scatter',\n", "              'uid': '54bd1094-cf30-4cef-94d6-3c894d2ca356',\n", "              'x': array([datetime.datetime(2025, 6, 8, 16, 0),\n", "                          datetime.datetime(2025, 6, 9, 17, 0),\n", "                          datetime.datetime(2025, 6, 9, 21, 15),\n", "                          datetime.datetime(2025, 7, 10, 16, 15),\n", "                          datetime.datetime(2025, 7, 10, 21, 15),\n", "                          datetime.datetime(2025, 7, 11, 9, 30),\n", "                          datetime.datetime(2025, 7, 14, 3, 30)], dtype=object),\n", "              'xaxis': 'x',\n", "              'y': array([106110., 108294., 110547., 112350., 115740., 118665., 121259.]),\n", "              'yaxis': 'y'},\n", "             {'customdata': array([[0.00000000e+00, 0.00000000e+00, 1.79472111e+02, 1.79651583e-02],\n", "                                   [1.00000000e+00, 1.00000000e+00, 1.83090633e+02, 1.80042463e-02],\n", "                                   [2.00000000e+00, 2.00000000e+00, 1.77385141e+02, 1.71350013e-02],\n", "                                   [3.00000000e+00, 3.00000000e+00, 2.01914108e+02, 1.91761880e-02],\n", "                                   [4.00000000e+00, 4.00000000e+00, 2.62768108e+02, 2.44865254e-02],\n", "                                   [5.00000000e+00, 5.00000000e+00, 1.88806268e+02, 1.71741415e-02],\n", "                                   [6.00000000e+00, 6.00000000e+00, 2.19951643e+02, 1.96697053e-02]]),\n", "              'hovertemplate': ('Exit Trade Id: %{customdata[0]' ... 'r>Return: %{customdata[3]:.2%}'),\n", "              'legendgroup': '3',\n", "              'marker': {'color': '#37B13F',\n", "                         'line': {'color': 'rgb(38,123,44)', 'width': 1},\n", "                         'opacity': array([0.76092882, 0.76144341, 0.75      , 0.77687175, 0.8467811 , 0.75051527,\n", "                                           0.78336879]),\n", "                         'size': array([ 7.3391362 ,  7.35510445,  7.        ,  7.83386672, 10.00324862,\n", "                                         7.01598958,  8.03547865]),\n", "                         'symbol': 'circle'},\n", "              'mode': 'markers',\n", "              'name': 'Closed - Profit',\n", "              'type': 'scatter',\n", "              'uid': '97a12b97-fea9-40a4-9eb3-3c671e4b1055',\n", "              'x': array([datetime.datetime(2025, 6, 8, 16, 0),\n", "                          datetime.datetime(2025, 6, 9, 17, 0),\n", "                          datetime.datetime(2025, 6, 9, 21, 15),\n", "                          datetime.datetime(2025, 7, 10, 16, 15),\n", "                          datetime.datetime(2025, 7, 10, 21, 15),\n", "                          datetime.datetime(2025, 7, 11, 9, 30),\n", "                          datetime.datetime(2025, 7, 14, 3, 30)], dtype=object),\n", "              'xaxis': 'x2',\n", "              'y': array([0.01796516, 0.01800425, 0.017135  , 0.01917619, 0.02448653, 0.01717414,\n", "                          0.01966971]),\n", "              'yaxis': 'y2'},\n", "             {'customdata': array([[ 7.00000000e+00,  7.00000000e+00, -3.25287835e+02, -2.85290505e-02]]),\n", "              'hovertemplate': ('Exit Trade Id: %{customdata[0]' ... 'r>Return: %{customdata[3]:.2%}'),\n", "              'legendgroup': '4',\n", "              'marker': {'color': '#FFAA00',\n", "                         'line': {'color': 'rgb(178,118,0)', 'width': 1},\n", "                         'opacity': array([0.9]),\n", "                         'size': array([11.65470317]),\n", "                         'symbol': 'circle'},\n", "              'mode': 'markers',\n", "              'name': 'Open',\n", "              'type': 'scatter',\n", "              'uid': '8a9fc91d-d478-41cc-8f7a-b901ee5b87b5',\n", "              'x': array([datetime.datetime(2025, 7, 28, 18, 0)], dtype=object),\n", "              'xaxis': 'x2',\n", "              'y': array([-0.02852905]),\n", "              'yaxis': 'y2'},\n", "             {'legendgroup': '5',\n", "              'line': {'color': '#7f7f7f'},\n", "              'name': '<PERSON><PERSON><PERSON>',\n", "              'showlegend': True,\n", "              'type': 'scatter',\n", "              'uid': 'cd2e37a7-a757-4c48-adfc-8fde8fc522ff',\n", "              'x': array([datetime.datetime(2025, 6, 6, 12, 0),\n", "                          datetime.datetime(2025, 6, 6, 12, 15),\n", "                          datetime.datetime(2025, 6, 6, 12, 30), ...,\n", "                          datetime.datetime(2025, 7, 28, 17, 30),\n", "                          datetime.datetime(2025, 7, 28, 17, 45),\n", "                          datetime.datetime(2025, 7, 28, 18, 0)], dtype=object),\n", "              'xaxis': 'x3',\n", "              'y': array([1.        , 0.99877799, 0.99734429, ..., 1.13489276, 1.13283362,\n", "                          1.13217932]),\n", "              'yaxis': 'y3'},\n", "             {'hoverinfo': 'skip',\n", "              'legendgroup': '6',\n", "              'line': {'color': 'rgba(0, 0, 0, 0)', 'width': 0},\n", "              'opacity': 0,\n", "              'showlegend': <PERSON><PERSON><PERSON>,\n", "              'type': 'scatter',\n", "              'uid': 'c20f15f9-0f4a-47d1-8551-1ee7b99d03ed',\n", "              'x': array([datetime.datetime(2025, 6, 6, 12, 0),\n", "                          datetime.datetime(2025, 6, 6, 12, 15),\n", "                          datetime.datetime(2025, 6, 6, 12, 30), ...,\n", "                          datetime.datetime(2025, 7, 28, 17, 30),\n", "                          datetime.datetime(2025, 7, 28, 17, 45),\n", "                          datetime.datetime(2025, 7, 28, 18, 0)], dtype=object),\n", "              'xaxis': 'x3',\n", "              'y': array([1, 1, 1, ..., 1, 1, 1]),\n", "              'yaxis': 'y3'},\n", "             {'connectgaps': <PERSON><PERSON><PERSON>,\n", "              'fill': 'tonexty',\n", "              'fillcolor': 'rgba(0, 128, 0, 0.3)',\n", "              'hoverinfo': 'skip',\n", "              'legendgroup': '6',\n", "              'line': {'color': 'rgba(0, 0, 0, 0)', 'width': 0},\n", "              'opacity': 0,\n", "              'showlegend': <PERSON><PERSON><PERSON>,\n", "              'type': 'scatter',\n", "              'uid': '6dc28737-f660-458a-bdc6-aa00db235740',\n", "              'x': array([datetime.datetime(2025, 6, 6, 12, 0),\n", "                          datetime.datetime(2025, 6, 6, 12, 15),\n", "                          datetime.datetime(2025, 6, 6, 12, 30), ...,\n", "                          datetime.datetime(2025, 7, 28, 17, 30),\n", "                          datetime.datetime(2025, 7, 28, 17, 45),\n", "                          datetime.datetime(2025, 7, 28, 18, 0)], dtype=object),\n", "              'xaxis': 'x3',\n", "              'y': array([1.        , 1.        , 1.        , ..., 1.11146745, 1.10945082,\n", "                          1.10881002]),\n", "              'yaxis': 'y3'},\n", "             {'hoverinfo': 'skip',\n", "              'legendgroup': '6',\n", "              'line': {'color': 'rgba(0, 0, 0, 0)', 'width': 0},\n", "              'opacity': 0,\n", "              'showlegend': <PERSON><PERSON><PERSON>,\n", "              'type': 'scatter',\n", "              'uid': '902ec65e-2abf-4e24-9764-4c4c5f38eeaa',\n", "              'x': array([datetime.datetime(2025, 6, 6, 12, 0),\n", "                          datetime.datetime(2025, 6, 6, 12, 15),\n", "                          datetime.datetime(2025, 6, 6, 12, 30), ...,\n", "                          datetime.datetime(2025, 7, 28, 17, 30),\n", "                          datetime.datetime(2025, 7, 28, 17, 45),\n", "                          datetime.datetime(2025, 7, 28, 18, 0)], dtype=object),\n", "              'xaxis': 'x3',\n", "              'y': array([1, 1, 1, ..., 1, 1, 1]),\n", "              'yaxis': 'y3'},\n", "             {'connectgaps': <PERSON><PERSON><PERSON>,\n", "              'fill': 'tonexty',\n", "              'fillcolor': 'rgba(255, 0, 0, 0.3)',\n", "              'hoverinfo': 'skip',\n", "              'legendgroup': '6',\n", "              'line': {'color': 'rgba(0, 0, 0, 0)', 'width': 0},\n", "              'opacity': 0,\n", "              'showlegend': <PERSON><PERSON><PERSON>,\n", "              'type': 'scatter',\n", "              'uid': '33721cdd-5178-45ed-ba88-5e4982745b38',\n", "              'x': array([datetime.datetime(2025, 6, 6, 12, 0),\n", "                          datetime.datetime(2025, 6, 6, 12, 15),\n", "                          datetime.datetime(2025, 6, 6, 12, 30), ...,\n", "                          datetime.datetime(2025, 7, 28, 17, 30),\n", "                          datetime.datetime(2025, 7, 28, 17, 45),\n", "                          datetime.datetime(2025, 7, 28, 18, 0)], dtype=object),\n", "              'xaxis': 'x3',\n", "              'y': array([0.998003  , 0.99678342, 0.99535259, ..., 1.        , 1.        ,\n", "                          1.        ]),\n", "              'yaxis': 'y3'},\n", "             {'legendgroup': '7',\n", "              'line': {'color': '#9467bd'},\n", "              'name': 'Value',\n", "              'showlegend': True,\n", "              'type': 'scatter',\n", "              'uid': '1264f90d-ffe0-470f-bcf1-3a2baab76ac5',\n", "              'x': array([datetime.datetime(2025, 6, 6, 12, 0),\n", "                          datetime.datetime(2025, 6, 6, 12, 15),\n", "                          datetime.datetime(2025, 6, 6, 12, 30), ...,\n", "                          datetime.datetime(2025, 7, 28, 17, 30),\n", "                          datetime.datetime(2025, 7, 28, 17, 45),\n", "                          datetime.datetime(2025, 7, 28, 18, 0)], dtype=object),\n", "              'xaxis': 'x3',\n", "              'y': array([0.998003  , 0.99678342, 0.99535259, ..., 1.11146745, 1.10945082,\n", "                          1.10881002]),\n", "              'yaxis': 'y3'},\n", "             {'hoverinfo': 'skip',\n", "              'legendgroup': '6',\n", "              'line': {'color': 'rgba(0, 0, 0, 0)', 'width': 0},\n", "              'opacity': 0.0,\n", "              'showlegend': <PERSON><PERSON><PERSON>,\n", "              'type': 'scatter',\n", "              'uid': '40c35d7b-91f5-49f5-ab97-4d9c55fabf04',\n", "              'x': array([datetime.datetime(2025, 6, 6, 12, 0),\n", "                          datetime.datetime(2025, 6, 6, 12, 15),\n", "                          datetime.datetime(2025, 6, 6, 12, 30), ...,\n", "                          datetime.datetime(2025, 7, 28, 17, 30),\n", "                          datetime.datetime(2025, 7, 28, 17, 45),\n", "                          datetime.datetime(2025, 7, 28, 18, 0)], dtype=object),\n", "              'xaxis': 'x3',\n", "              'y': array([1, 1, 1, ..., 1, 1, 1]),\n", "              'yaxis': 'y3'}],\n", "    'layout': {'annotations': [{'font': {'size': 16},\n", "                                'showarrow': <PERSON><PERSON><PERSON>,\n", "                                'text': 'Orders',\n", "                                'x': 0.5,\n", "                                'xanchor': 'center',\n", "                                'xref': 'paper',\n", "                                'y': 1.0,\n", "                                'yanchor': 'bottom',\n", "                                'yref': 'paper'},\n", "                               {'font': {'size': 16},\n", "                                'showarrow': <PERSON><PERSON><PERSON>,\n", "                                'text': 'Trade PnL',\n", "                                'x': 0.5,\n", "                                'xanchor': 'center',\n", "                                'xref': 'paper',\n", "                                'y': 0.6405228758169934,\n", "                                'yanchor': 'bottom',\n", "                                'yref': 'paper'},\n", "                               {'font': {'size': 16},\n", "                                'showarrow': <PERSON><PERSON><PERSON>,\n", "                                'text': 'Cumulative Returns',\n", "                                'x': 0.5,\n", "                                'xanchor': 'center',\n", "                                'xref': 'paper',\n", "                                'y': 0.28104575163398693,\n", "                                'yanchor': 'bottom',\n", "                                'yref': 'paper'}],\n", "               'height': 510,\n", "               'legend': {'orientation': 'h',\n", "                          'traceorder': 'normal',\n", "                          'x': 1,\n", "                          'xanchor': 'right',\n", "                          'y': 1.0784313725490196,\n", "                          'yanchor': 'bottom'},\n", "               'margin': {'b': 30, 'l': 30, 'r': 30, 't': 30},\n", "               'shapes': [{'line': {'color': 'gray', 'dash': 'dash'},\n", "                           'type': 'line',\n", "                           'x0': 0.0,\n", "                           'x1': 1.0,\n", "                           'xref': 'paper',\n", "                           'y0': 0,\n", "                           'y1': 0,\n", "                           'yref': 'y2'},\n", "                          {'line': {'color': 'gray', 'dash': 'dash'},\n", "                           'type': 'line',\n", "                           'x0': 0.0,\n", "                           'x1': 1.0,\n", "                           'xref': 'paper',\n", "                           'y0': 1,\n", "                           'y1': 1,\n", "                           'yref': 'y3'}],\n", "               'showlegend': True,\n", "               'template': '...',\n", "               'width': 1250,\n", "               'xaxis': {'anchor': 'y', 'domain': [0.0, 1.0], 'matches': 'x3', 'showticklabels': False},\n", "               'xaxis2': {'anchor': 'y2', 'domain': [0.0, 1.0], 'matches': 'x3', 'showticklabels': False},\n", "               'xaxis3': {'anchor': 'y3', 'domain': [0.0, 1.0], 'title': {'text': 'Index'}},\n", "               'yaxis': {'anchor': 'x', 'domain': [0.7189542483660131, 1.0], 'title': {'text': 'Price'}},\n", "               'yaxis2': {'anchor': 'x2',\n", "                          'domain': [0.35947712418300654, 0.6405228758169934],\n", "                          'tickformat': '.2%',\n", "                          'title': {'text': 'Trade PnL'}},\n", "               'yaxis3': {'anchor': 'x3', 'domain': [0.0, 0.28104575163398693], 'title': {'text': 'Cumulative returns'}}}\n", "})"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["import vectorbt as vbt\n", "vbt.settings.set_theme('dark')\n", "# vbt.settings['plotting']['layout']['width'] = 1200\n", "# vbt.settings['plotting']['layout']['height'] = 200\n", "pf = vbt.Portfolio.from_signals(\n", "    close=data[\"close\"],\n", "    init_cash=10000,\n", "    fees=0.001,\n", "    slippage=0.001,\n", "    freq=\"1h\",\n", "    tp_stop=0.02,\n", "    )\n", " \n", "# To print available subplots for a VectorBT Portfolio, use:\n", "print(\"Available subplots:\", pf.subplots.keys())\n", "pf.plot()\n", "\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "d9d73e4a", "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "\n", "# Add the parent directory to sys.path so we can import exchange\n", "# In Jupyter, __file__ is not defined; use os.getcwd() instead:\n", "sys.path.append(os.path.abspath(os.path.join(os.getcwd(), '..')))\n", "\n", "from exchange import hyperliquid, fetch_and_prepare_data\n", "\n", "data = hyperliquid.fetch_ohlcv(\"BTC/USDC:USDC\", \"1h\", \"2023-01-01\", limit=1000)\n", "\n", "data.head()\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}