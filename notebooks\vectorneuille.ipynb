import pandas as pd
import pandas_ta as ta # utilisé via df.ta
import vectorbt as vbt
import numpy as np

from exchange import fetch_and_prepare_data

# ===== CONFIGURATION =====
# Mean reversion strategy parameters
BBANDS_PERIOD = 20         # Period for Bollinger Bands
BBANDS_STD = 2.0           # Standard deviation for Bollinger Bands
ADX_PERIOD = 14            # Period for ADX calculation
ADX_THRESHOLD = 20         # ADX threshold for trend strength
ADX_THRESHOLD_FILTER = 60  # Filter to avoid trading in directional markets
SMA_PERIOD = 200           # Period for SMA calculation
SMA_BOUNCE_THRESHOLD = 0.002  # Threshold for price bounce from SMA (0.2%)
INITIAL_ENTRY_SIZE = 0.01  # Initial position size (1% of portfolio)
DCA_SIZE_INCREMENT = 0.01  # Increment for each DCA (1% additional)
MAX_DCA_SIZE = 0.10  # Maximum DCA size (10% of portfolio)
EXIT_SIZE = 1.0            # Exit size (100% of position)
TF = '1h'
# Portfolio settings
INITIAL_CASH = 500000
FEE = 0.0004

# ===== DATA PREPARATION =====
symbol = "BTC/USDC:USDC"
data = fetch_and_prepare_data(symbol, TF)
# data = pd.read_csv("data/BTCUSD_1h_Combined_Index.csv").iloc[:,:5][90000:]

data.columns = [c.lower() for c in data.columns]

print(data)

required_cols = ['close', 'low', 'high']
if not all(col in data.columns for col in required_cols):
    raise ValueError(f"Missing required columns. Got: {data.columns.tolist()}")



# ===== TECHNICAL INDICATORS =====
# Add indicators using pandas_ta
# 1. Bollinger Bands
data.ta.bbands(length=BBANDS_PERIOD, std=BBANDS_STD, append=True)

# 2. ADX - Average Directional Index for trend strength
data.ta.adx(length=ADX_PERIOD, append=True)

# 3. SMA 200 - Support/Resistance level
data.ta.sma(length=SMA_PERIOD, append=True)

# Clean up NaN values
data.dropna(inplace=True)
data.reset_index(drop=True, inplace=True)  # Reset index after dropping NaNs

# Define column names for readability
bbl_col = f'BBL_{BBANDS_PERIOD}_{BBANDS_STD}'
bbm_col = f'BBM_{BBANDS_PERIOD}_{BBANDS_STD}'
bbu_col = f'BBU_{BBANDS_PERIOD}_{BBANDS_STD}'
adx_col = f'ADX_{ADX_PERIOD}'
sma_col = f'SMA_{SMA_PERIOD}'

print(data)

# Print sample of calculated indicators
print("\n===== Technical Indicators =====")
indicator_cols = [bbl_col, bbm_col, bbu_col, adx_col, sma_col]
print(data[['close'] + indicator_cols].tail(20))

# ===== ENTRY/EXIT SIGNALS =====
# --- LONG & SHORT STRATEGIES ---
# Trend condition for adaptive exits
weak_trend = data[adx_col] < ADX_THRESHOLD
strong_trend = ~weak_trend  # Opposite of weak trend
# Filter condition for high ADX - avoid trading in strongly directional markets
high_adx_filter = data[adx_col] >= ADX_THRESHOLD_FILTER

# Define entry conditions
# Initial entries: Price crosses from below to above the lower band for longs
# AND ADX is below the filter threshold
long_initial_entries = (data['close'].shift(1) < data[bbl_col].shift(1)) & (data['close'] >= data[bbl_col]) & (~high_adx_filter)

# DCA conditions for longs: Price touches or goes below lower band
# AND ADX is below the filter threshold
long_dca_conditions = (data['low'] <= data[bbl_col]) & (~high_adx_filter)

# Initial entries: Price crosses from above to below the upper band for shorts
# AND ADX is below the filter threshold
short_initial_entries = (data['close'].shift(1) > data[bbu_col].shift(1)) & (data['close'] <= data[bbu_col]) & (~high_adx_filter)

# DCA conditions for shorts: Price touches or goes above upper band
# AND ADX is below the filter threshold
short_dca_conditions = (data['high'] >= data[bbu_col]) & (~high_adx_filter)

# Initialize arrays for position tracking
long_position = pd.Series(0, index=data.index)  # 0 = no position, 1 = in position
short_position = pd.Series(0, index=data.index)  # 0 = no position, 1 = in position

# Initialize signal arrays
long_entries = pd.Series(False, index=data.index)
short_entries = pd.Series(False, index=data.index)
long_exits = pd.Series(False, index=data.index)
short_exits = pd.Series(False, index=data.index)

# Track positions, DCA opportunities, and generate clean signals in a single pass
for i in range(len(data)):
    # Carry forward position state from previous bar (if not first bar)
    if i > 0:
        long_position.iloc[i] = long_position.iloc[i-1]
        short_position.iloc[i] = short_position.iloc[i-1]
    
    # Define exit conditions - UPDATED to exit on:
    # 1. Price reaching the opposite band
    # 2. Price reaching middle line if ADX is low (weak trend)
    # 3. ADX exceeding the filter threshold (strong directional market)
    long_exit_condition = (data['close'].iloc[i] >= data[bbu_col].iloc[i]) | \
                          ((data['close'].iloc[i] >= data[bbm_col].iloc[i]) & weak_trend.iloc[i]) | \
                          high_adx_filter.iloc[i]  # Exit if ADX exceeds filter threshold
    
    short_exit_condition = (data['close'].iloc[i] <= data[bbl_col].iloc[i]) | \
                           ((data['close'].iloc[i] <= data[bbm_col].iloc[i]) & weak_trend.iloc[i]) | \
                           high_adx_filter.iloc[i]  # Exit if ADX exceeds filter threshold
    
    # Process positions in priority order: exits first, then entries
    
    # Process exits (only if we have a position)
    if long_position.iloc[i] > 0 and long_exit_condition:
        long_exits.iloc[i] = True
        long_position.iloc[i] = 0  # Close position
    
    if short_position.iloc[i] > 0 and short_exit_condition:
        short_exits.iloc[i] = True
        short_position.iloc[i] = 0  # Close position
    
    # Process initial entries (only if we don't have a position)
    if long_position.iloc[i] == 0 and long_initial_entries.iloc[i]:
        long_entries.iloc[i] = True
        long_position.iloc[i] = 1  # Open position
    
    if short_position.iloc[i] == 0 and short_initial_entries.iloc[i]:
        short_entries.iloc[i] = True
        short_position.iloc[i] = 1  # Open position
    
    # Process DCA opportunities (only if we already have a position and no exit signal on same bar)
    if long_position.iloc[i] > 0 and not long_exits.iloc[i] and long_dca_conditions.iloc[i]:
        # Only add DCA if we didn't already have an entry on this bar
        if not long_entries.iloc[i]:
            long_entries.iloc[i] = True  # Add to position
    
    if short_position.iloc[i] > 0 and not short_exits.iloc[i] and short_dca_conditions.iloc[i]:
        # Only add DCA if we didn't already have an entry on this bar
        if not short_entries.iloc[i]:
            short_entries.iloc[i] = True  # Add to position

# Count entries and exits by type
long_initial_count = sum(long_initial_entries & long_entries)
long_dca_count = sum(long_entries) - long_initial_count
short_initial_count = sum(short_initial_entries & short_entries)
short_dca_count = sum(short_entries) - short_initial_count


# Create simplified entry tracking
entry_counts = pd.DataFrame({
    'Count': [
        sum(long_entries),
        sum(short_entries)
    ]
}, index=['Long', 'Short'])

# Plot distribution of long vs short entries
# entry_counts.plot.pie(y='Count', autopct='%1.1f%%')
 
# Print signal statistics
print("\n===== Signal Statistics =====")
print(f"Long entries: {long_initial_count}")
print(f"Long exits: {sum(long_exits)}")
print(f"Short entries: {short_initial_count}")
print(f"Short exits: {sum(short_exits)}")


# ===== CREATE VARIABLE SIZING =====
# Create arrays for position sizing that vary between entries and exits
# For initial entries: use INITIAL_ENTRY_SIZE (1%)
# For DCA entries: start with INITIAL_ENTRY_SIZE (1%) and increment by DCA_SIZE_INCREMENT (1%) 
# until reaching MAX_DCA_SIZE (10%)
# For exits: use EXIT_SIZE (100%)
size_array = pd.Series(INITIAL_ENTRY_SIZE, index=data.index)

# Track DCA counts for long and short positions to increment sizing
long_dca_count = 0
short_dca_count = 0
long_in_position = False
short_in_position = False

# Create arrays to track portfolio usage
portfolio_pct_used = pd.Series(0.0, index=data.index)  # Track total portfolio % used
long_pct_used = pd.Series(0.0, index=data.index)      # Track % used for long positions
short_pct_used = pd.Series(0.0, index=data.index)     # Track % used for short positions

# Process each bar to set appropriate sizing
for i in range(len(data)):
    # Carry forward usage values from previous bar (if not first bar)
    if i > 0:
        long_pct_used.iloc[i] = long_pct_used.iloc[i-1]
        short_pct_used.iloc[i] = short_pct_used.iloc[i-1]
        portfolio_pct_used.iloc[i] = long_pct_used.iloc[i] + short_pct_used.iloc[i]
    
    # Check for exits first (reset DCA counters and portfolio usage)
    if long_exits.iloc[i]:
        size_array.iloc[i] = EXIT_SIZE
        long_dca_count = 0
        long_in_position = False
        long_pct_used.iloc[i] = 0.0  # Reset long usage on exit
    
    elif short_exits.iloc[i]:
        size_array.iloc[i] = EXIT_SIZE
        short_dca_count = 0
        short_in_position = False
        short_pct_used.iloc[i] = 0.0  # Reset short usage on exit
    
    # Check for entries
    elif long_entries.iloc[i]:
        if not long_in_position:  # Initial entry
            size_array.iloc[i] = INITIAL_ENTRY_SIZE
            long_in_position = True
            long_pct_used.iloc[i] = INITIAL_ENTRY_SIZE  # Initial usage
        else:  # DCA entry
            long_dca_count += 1
            # Calculate size, but cap at MAX_DCA_SIZE
            dca_size = min(INITIAL_ENTRY_SIZE + (DCA_SIZE_INCREMENT * long_dca_count), MAX_DCA_SIZE)
            size_array.iloc[i] = dca_size
            long_pct_used.iloc[i] += dca_size  # Add to usage
    
    elif short_entries.iloc[i]:
        if not short_in_position:  # Initial entry
            size_array.iloc[i] = INITIAL_ENTRY_SIZE
            short_in_position = True
            short_pct_used.iloc[i] = INITIAL_ENTRY_SIZE  # Initial usage
        else:  # DCA entry
            short_dca_count += 1
            # Calculate size, but cap at MAX_DCA_SIZE
            dca_size = min(INITIAL_ENTRY_SIZE + (DCA_SIZE_INCREMENT * short_dca_count), MAX_DCA_SIZE)
            size_array.iloc[i] = dca_size
            short_pct_used.iloc[i] += dca_size  # Add to usage
    
    # Update total portfolio usage
    portfolio_pct_used.iloc[i] = long_pct_used.iloc[i] + short_pct_used.iloc[i]

# Print summary of portfolio usage
print("\n===== Portfolio Usage Statistics =====")
print(f"Maximum portfolio percentage used: {portfolio_pct_used.max():.2%}")
print(f"Maximum long percentage used: {long_pct_used.max():.2%}")
print(f"Maximum short percentage used: {short_pct_used.max():.2%}")

# ===== PORTFOLIO CREATION =====
# Create a single portfolio that handles both long and short positions using vectorbt's built-in functionality
portfolio = vbt.Portfolio.from_signals(
    close=data['close'],
    entries=long_entries,                # Long entries
    exits=long_exits,                    # Long exits
    short_entries=short_entries,         # Short entries
    short_exits=short_exits,             # Short exits
    size=size_array,                     # Variable position size based on SMA proximity
    size_type='percent',                 # Size as percentage of portfolio
    init_cash=INITIAL_CASH,
    fees=FEE,
    freq=TF,
    sl_stop=0.04,
    accumulate=True                    # Disable DCA (mean reversion strategy uses single entries)
)

# ===== STATS =====
print("\n===== Portfolio Stats =====")
stats = portfolio.stats()

def calculate_beta(strategy_ret: pd.Series,
                   benchmark_ret: pd.Series) -> float:
    """
    Beta = Cov(strategy, benchmark) / Var(benchmark).

    Les deux séries doivent être déjà exprimées en rendements
    (pourcentage ou log-retours) et indexées sur les mêmes dates.
    """
    aligned = pd.concat([strategy_ret, benchmark_ret], axis=1).dropna()
    if aligned.shape[0] < 2:
        return np.nan
    cov = np.cov(aligned.iloc[:, 0], aligned.iloc[:, 1])[0, 1]
    var = np.var(aligned.iloc[:, 1])
    return cov / var if var > 0 else np.nan

# Calcul du beta et ajout aux stats
strategy_returns = portfolio.returns()
asset_returns = data['close'].pct_change()
beta = calculate_beta(strategy_returns, asset_returns)

# Création d'une série pandas pour le beta avec le même format que les autres stats
beta_stat = pd.Series(beta, index=['Beta vs Benchmark'])

stats = pd.concat([stats, beta_stat])

print(stats)

# Analyze order execution to see if positions close in a single bar
print("\n===== Portfolio Trade Analysis =====")
print(f"Total trades: {len(portfolio.trades)}")

vbt.settings.set_theme('dark')
fig = portfolio.plot(subplot_settings={'orders': {'close_trace_kwargs': {'visible': False}}})
fig = data.vbt.ohlcv.plot(plot_type='candlestick', fig=fig, show_volume=False, xaxis_rangeslider_visible=False)
fig = data[[bbl_col, bbm_col, bbu_col, sma_col]].rename(
    columns={
        bbl_col: 'Lower BB', 
        bbm_col: 'Middle BB', 
        bbu_col: 'Upper BB',
        sma_col: 'SMA 200'
    }
).vbt.plot(fig=fig)

fig.update_layout(width=None, height=800, title_text=f"Bollinger Bands + SMA 200 Strategy - {symbol}").show()


# ==== INDIVIDUAL TRADE ANALYSIS ====

# Plot trade analysis for both long and short positions
portfolio.plot(
    subplots=['trades', 'trade_pnl'],
    subplot_settings={
        'trades': dict(title='Trades Timeline'),
        'trade_pnl': dict(title='Individual Trade P&L')
    }
).update_layout(
    width=None,
    height=None,
    title_text="Bollinger Bands Mean Reversion Strategy - Trade Performance Analysis"
).show()