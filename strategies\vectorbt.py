#!/usr/bin/env python3
"""
VectorBT Bollinger Bands Mean Reversion Strategy - Functional Implementation
Bollinger Bands mean reversion with ADX filtering.
"""

from typing import Dict, List
import pandas as pd
import pandas_ta as ta
from base import BaseStrategy, Signals, StrategyConfig


def create_bollinger_mean_reversion_signals(df: pd.DataFrame, **params) -> Signals:
    """Create Bollinger Bands mean reversion signals faithful to vect.py implementation.

    Args:
        df: OHLC DataFrame
        **params: Strategy parameters
            - bbands_period: Bollinger Bands period (default: 20)
            - bbands_std: Bollinger Bands standard deviation (default: 2.0)
            - adx_period: ADX period (default: 14)
            - adx_threshold: ADX threshold for trend strength (default: 20)
            - adx_threshold_filter: ADX filter to avoid strong trends (default: 60)

    Returns:
        Signals object with entries and exits
    """
    bbands_period = params.get('bbands_period', 20)
    bbands_std = params.get('bbands_std', 2.0)
    adx_period = params.get('adx_period', 14)
    adx_threshold = params.get('adx_threshold', 20)
    adx_threshold_filter = params.get('adx_threshold_filter', 60)

    # Calculate Bollinger Bands
    bb = ta.bbands(df['close'], length=bbands_period, std=bbands_std)
    bb_lower = bb[f'BBL_{bbands_period}_{bbands_std}']
    bb_upper = bb[f'BBU_{bbands_period}_{bbands_std}']
    bb_middle = bb[f'BBM_{bbands_period}_{bbands_std}']

    # Calculate ADX for trend strength
    adx = ta.adx(df['high'], df['low'], df['close'], length=adx_period)
    adx_value = adx[f'ADX_{adx_period}']

    # Trend conditions (faithful to vect.py)
    weak_trend = adx_value < adx_threshold
    # Filter condition for high ADX - avoid trading in strongly directional markets
    high_adx_filter = adx_value >= adx_threshold_filter

    # Entry conditions (faithful to vect.py crossover logic)
    # Initial entries: Price crosses from below to above the lower band for longs
    # AND ADX is below the filter threshold
    long_initial_entries = (df['close'].shift(1) < bb_lower.shift(1)) & (df['close'] >= bb_lower) & (~high_adx_filter)

    # DCA conditions for longs: Price touches or goes below lower band
    # AND ADX is below the filter threshold
    long_dca_conditions = (df['low'] <= bb_lower) & (~high_adx_filter)

    # Initial entries: Price crosses from above to below the upper band for shorts
    # AND ADX is below the filter threshold
    short_initial_entries = (df['close'].shift(1) > bb_upper.shift(1)) & (df['close'] <= bb_upper) & (~high_adx_filter)

    # DCA conditions for shorts: Price touches or goes above upper band
    # AND ADX is below the filter threshold
    short_dca_conditions = (df['high'] >= bb_upper) & (~high_adx_filter)

    # Initialize position tracking arrays
    long_position = pd.Series(0, index=df.index)
    short_position = pd.Series(0, index=df.index)

    # Initialize signal arrays
    long_entries = pd.Series(False, index=df.index)
    short_entries = pd.Series(False, index=df.index)
    long_exits = pd.Series(False, index=df.index)
    short_exits = pd.Series(False, index=df.index)

    # Track positions, DCA opportunities, and generate clean signals (faithful to vect.py)
    for i in range(len(df)):
        # Carry forward position state from previous bar (if not first bar)
        if i > 0:
            long_position.iloc[i] = long_position.iloc[i-1]
            short_position.iloc[i] = short_position.iloc[i-1]
            # Prevent overlapping exposure – close the opposite side if a fresh initial entry appears
            if long_position.iloc[i] > 0 and short_initial_entries.iloc[i]:
                long_exits.iloc[i] = True
                long_position.iloc[i] = 0  # Close long before opening short
            elif short_position.iloc[i] > 0 and long_initial_entries.iloc[i]:
                short_exits.iloc[i] = True
                short_position.iloc[i] = 0  # Close short before opening long

        # Define exit conditions (faithful to vect.py) - exit on:
        # 1. Price reaching the opposite band
        # 2. Price reaching middle line if ADX is low (weak trend)
        # 3. ADX exceeding the filter threshold (strong directional market)
        long_exit_condition = (df['close'].iloc[i] >= bb_upper.iloc[i]) | \
                              ((df['close'].iloc[i] >= bb_middle.iloc[i]) & weak_trend.iloc[i]) | \
                              high_adx_filter.iloc[i]

        short_exit_condition = (df['close'].iloc[i] <= bb_lower.iloc[i]) | \
                               ((df['close'].iloc[i] <= bb_middle.iloc[i]) & weak_trend.iloc[i]) | \
                               high_adx_filter.iloc[i]

        # Process exits (only if we have a position)
        if long_position.iloc[i] > 0 and long_exit_condition:
            long_exits.iloc[i] = True
            long_position.iloc[i] = 0  # Close position

        if short_position.iloc[i] > 0 and short_exit_condition:
            short_exits.iloc[i] = True
            short_position.iloc[i] = 0  # Close position

        # Process initial entries (only if we don't have a position)
        if long_position.iloc[i] == 0 and long_initial_entries.iloc[i]:
            long_entries.iloc[i] = True
            long_position.iloc[i] = 1  # Open position

        if short_position.iloc[i] == 0 and short_initial_entries.iloc[i]:
            short_entries.iloc[i] = True
            short_position.iloc[i] = 1  # Open position

        # Process DCA opportunities (only if we already have a position and no exit signal on same bar)
        if long_position.iloc[i] > 0 and not long_exits.iloc[i] and long_dca_conditions.iloc[i]:
            # Only add DCA if we didn't already have an entry on this bar
            if not long_entries.iloc[i]:
                long_entries.iloc[i] = True  # Add to position

        if short_position.iloc[i] > 0 and not short_exits.iloc[i] and short_dca_conditions.iloc[i]:
            # Only add DCA if we didn't already have an entry on this bar
            if not short_entries.iloc[i]:
                short_entries.iloc[i] = True  # Add to position

    return Signals(
        entries=long_entries,
        exits=long_exits,
        short_entries=short_entries,
        short_exits=short_exits
    )


class VectorBTStrategy(BaseStrategy):
    """VectorBT Strategy - Functional wrapper for backward compatibility."""
    
    def __init__(self, config: StrategyConfig):
        super().__init__(config)
        self.signal_params = config.parameters.copy()
    
    def get_required_timeframes(self) -> List[str]:
        return self.get_parameter('required_timeframes', ['1h'])
    
    def get_required_columns(self) -> List[str]:
        return ['open', 'high', 'low', 'close']
    
    def generate_signals(self, tf_data: Dict[str, pd.DataFrame]) -> Signals:
        """Generate Bollinger Bands signals using functional approach."""
        if not tf_data:
            empty_series = pd.Series(False, index=pd.Index([]))
            return Signals(empty_series, empty_series, empty_series, empty_series)
        
        # Use primary timeframe
        primary_tf = self.signal_params.get('primary_timeframe', list(tf_data.keys())[0])
        if primary_tf not in tf_data:
            primary_tf = list(tf_data.keys())[0]
        
        primary_df = tf_data[primary_tf]
        return create_bollinger_mean_reversion_signals(primary_df, **self.signal_params)
